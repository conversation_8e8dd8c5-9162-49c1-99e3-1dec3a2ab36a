<?php
#region region DOCS

/** @var Activo|null $activo */
/** @var array $imagenes */
/** @var string $error_display */
/** @var string $error_text */

use App\classes\Activo;

#endregion DOCS
?>
<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
	<meta charset="utf-8"/>
	<title><?php echo APP_NAME; ?> | Información del Activo</title>
	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
	<meta content="" name="description"/>
	<meta content="" name="author"/>
	
	<?php #region HEAD ?>
	<?php require_once __ROOT__ . '/views/general/head.view.php'; ?>
	<?php #endregion HEAD ?>

	<!-- Custom styles for scanactivo -->
	<style>
		.image-container {
			position: relative;
			overflow: hidden;
			border-radius: 8px;
			transition: transform 0.2s ease;
		}

		.image-container:hover {
			transform: scale(1.02);
		}

		.image-container img {
			transition: opacity 0.2s ease;
		}

		.image-container:hover img {
			opacity: 0.9;
		}

		.form-control-static {
			padding: 8px 0;
			margin-bottom: 0;
			min-height: 20px;
			word-wrap: break-word;
		}

		.form-label {
			margin-bottom: 5px;
			color: #495057;
		}
	</style>
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
	<span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed">
	<!-- #header -->
	<?php require_once __ROOT__ . '/views/general/header.view.php'; ?>
	
	<!-- #sidebar -->
	<?php require_once __ROOT__ . '/views/general/sidebar.view.php'; ?>
	
	<!-- BEGIN #content -->
	<div id="content" class="app-content">
		<?php #region region PAGE HEADER ?>
		<div class="d-flex align-items-center mb-3">
			<div>
				<h4 class="mb-0">Información del Activo</h4>
				<p class="mb-0 text-muted">Detalles del activo escaneado</p>
			</div>
		</div>
		
		<hr>
		<?php #endregion PAGE HEADER ?>
		
		<?php if ($error_display === 'show'): ?>
		<div class="alert alert-danger alert-dismissible fade show">
			<strong>Error!</strong> <?php echo $error_text; ?>
			<button type="button" class="btn-close" data-bs-dismiss="alert"></button>
		</div>
		<?php endif; ?>
		
		<?php if ($activo): ?>
		<?php #region region PANEL ACTIVO ?>
		<div class="panel panel-inverse mt-3 no-border-radious">
			<div class="panel-heading no-border-radious">
				<h4 class="panel-title">
					Información del Activo
				</h4>
			</div>
			<!-- BEGIN PANEL body -->
			<div class="panel-body">
				<!-- Basic Information -->
				<div class="row">
					<div class="col-md-12">
						<div class="mb-3">
							<label class="form-label"><strong>Descripción:</strong></label>
							<p class="form-control-static"><?php echo htmlspecialchars($activo->getDescripcion() ?? ''); ?></p>
						</div>
					</div>
				</div>

				<!-- Brand and Model -->
				<div class="row">
					<div class="col-md-6">
						<div class="mb-3">
							<label class="form-label"><strong>Marca:</strong></label>
							<p class="form-control-static"><?php echo htmlspecialchars($activo->getMarca() ?? 'No especificada'); ?></p>
						</div>
					</div>
					<div class="col-md-6">
						<div class="mb-3">
							<label class="form-label"><strong>Modelo:</strong></label>
							<p class="form-control-static"><?php echo htmlspecialchars($activo->getModelo() ?? 'No especificado'); ?></p>
						</div>
					</div>
				</div>

				<!-- Serial Number and Acquisition Date -->
				<div class="row">
					<div class="col-md-6">
						<div class="mb-3">
							<label class="form-label"><strong>Número de Serie:</strong></label>
							<p class="form-control-static"><?php echo htmlspecialchars($activo->getNumeroSerie() ?? 'No especificado'); ?></p>
						</div>
					</div>
					<div class="col-md-6">
						<div class="mb-3">
							<label class="form-label"><strong>Fecha de Adquisición:</strong></label>
							<p class="form-control-static">
								<?php
								if ($activo->getFechaAdquisicion()) {
									$fecha = DateTime::createFromFormat('Y-m-d', $activo->getFechaAdquisicion());
									echo $fecha ? $fecha->format('d/m/Y') : $activo->getFechaAdquisicion();
								} else {
									echo 'No especificada';
								}
								?>
							</p>
						</div>
					</div>
				</div>

				<!-- Asset Value -->
				<div class="row">
					<div class="col-md-6">
						<div class="mb-3">
							<label class="form-label"><strong>Valor del Activo:</strong></label>
							<p class="form-control-static">
								<?php echo $activo->getValorActivo() ? '$' . number_format($activo->getValorActivo(), 0, '', '.') : 'No especificado'; ?>
							</p>
						</div>
					</div>
				</div>

				<!-- Characteristics -->
				<div class="row">
					<div class="col-md-12">
						<div class="mb-3">
							<label class="form-label"><strong>Características:</strong></label>
							<p class="form-control-static"><?php echo nl2br(htmlspecialchars($activo->getCaracteristicas() ?? 'No especificadas')); ?></p>
						</div>
					</div>
				</div>

				<!-- Comments -->
				<div class="row">
					<div class="col-md-12">
						<div class="mb-3">
							<label class="form-label"><strong>Comentarios:</strong></label>
							<p class="form-control-static"><?php echo nl2br(htmlspecialchars($activo->getComentarios() ?? 'Sin comentarios')); ?></p>
						</div>
					</div>
				</div>
			</div>
			<!-- END PANEL body -->
		</div>
		<?php #endregion PANEL ACTIVO ?>

		<?php #region region IMAGES ?>
		<div class="panel panel-inverse mt-3 no-border-radious">
			<div class="panel-heading no-border-radious">
				<h4 class="panel-title">
					Imágenes del Activo
				</h4>
			</div>
			<!-- BEGIN PANEL body -->
			<div class="panel-body">
				<?php if (empty($imagenes)): ?>
					<div class="alert alert-info">
						<i class="fa fa-info-circle me-2"></i> Este activo no tiene imágenes asociadas.
					</div>
				<?php else: ?>
					<div class="row">
						<?php foreach ($imagenes as $imagen): ?>
							<div class="col-md-3 col-sm-4 col-6 mb-3">
								<div class="image-container text-center">
									<img src="<?php echo RUTA; ?>resources/uploads/activos/<?php echo htmlspecialchars($imagen->getNombreArchivo()); ?>"
										 alt="Imagen de activo" class="img-fluid img-thumbnail" style="max-height: 200px; width: auto;">
									<div class="image-caption text-center mt-2">
										<small class="text-muted"><?php echo htmlspecialchars($imagen->getNombreArchivo()); ?></small>
									</div>
								</div>
							</div>
						<?php endforeach; ?>
					</div>
				<?php endif; ?>
			</div>
			<!-- END PANEL body -->
		</div>
		<?php #endregion IMAGES ?>
		<?php endif; ?>
	</div>
	<!-- END #content -->
	
	
	<!-- BEGIN scroll-top-btn -->
	<a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
	<!-- END scroll-top-btn -->
</div>
<!-- END #app -->

<?php #region region JS ?>
<?php require_once __ROOT__ . '/views/general/core_js.view.php'; ?>
<?php #endregion JS ?>

</body>
</html>
