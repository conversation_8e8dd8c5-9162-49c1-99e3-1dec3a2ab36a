<?php

// Iniciar sesión si es necesario
use App\classes\Activo;
use App\classes\ActivoImagen;

if (session_status() === PHP_SESSION_NONE) {
	session_start();
}

/** @var PDO $conexion */
global $conexion;

require_once dirname(__FILE__, 2) . '/config/config.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/general/preparar.php';

// Check for valid database connection before proceeding
if (!$conexion instanceof PDO) {
	error_log("Error crítico: No hay conexión a la base de datos en scanactivo.php.");
	$response['message'] = 'Error crítico: No se pudo conectar a la base de datos.';
	http_response_code(503); // Service Unavailable
	echo json_encode($response);
	exit;
}

#region region init variables
$activo = null;
$imagenes = [];
$error_display = '';
$error_text = '';
#endregion init variables

#region region Handle GET request
if ($_SERVER['REQUEST_METHOD'] == 'GET') {
    // Check if ID parameter is provided
    if (!isset($_GET['id']) || empty($_GET['id'])) {
        // No ID provided, set error message and redirect to dashboard
        $_SESSION['flash_message_error'] = 'El ID del activo no fue proporcionado.';
        header('Location: dashboard');
        exit;
    }
    
    $id = filter_input(INPUT_GET, 'id', FILTER_VALIDATE_INT);
    
    if (!$id) {
        // Invalid ID format, set error message and redirect to dashboard
        $_SESSION['flash_message_error'] = 'El formato del ID del activo es inválido.';
        header('Location: dashboard');
        exit;
    }
    
    try {
        // Get activo information
        $activo = Activo::get($id, $conexion);

        if (!$activo) {
            // Activo not found, set error message and redirect to dashboard
            $_SESSION['flash_message_error'] = 'El activo con ID ' . $id . ' no fue encontrado.';
            header('Location: dashboard');
            exit;
        }

        // Get images associated with this activo
        $imagenes = ActivoImagen::get_por_activo($id, $conexion);

    } catch (Exception $e) {
        // Error retrieving activo, set error message
        $error_display = 'show';
        $error_text = 'Error al obtener información del activo: ' . $e->getMessage();
    }
}
#endregion Handle GET request

require_once __ROOT__ . '/views/scanactivo.view.php';

?>
