<?php

declare(strict_types=1);

if (session_status() === PHP_SESSION_NONE) {
	session_start();
}

use App\classes\Activo;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Font;
use PhpOffice\PhpSpreadsheet\Style\Fill;

/** @var PDO $conexion */
global $conexion;

// Include necessary files
require_once dirname(__FILE__, 2) . '/config/config.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/general/preparar.php';

// Check for valid database connection before proceeding
if (!$conexion instanceof PDO) {
	error_log("Error crítico: No hay conexión a la base de datos en export_activos.php.");
	http_response_code(503); // Service Unavailable
	echo json_encode(['message' => 'Error crítico: No se pudo conectar a la base de datos.']);
	exit;
}

try {
    // Get all active assets using the existing get_list method
    $activos = Activo::get_list($conexion);
    
    // Create a new Spreadsheet object
    $spreadsheet = new Spreadsheet();
    $sheet = $spreadsheet->getActiveSheet();
    $sheet->setTitle('Activos');
    
    // --- Add Title Row ---
    $currentDate = date('Y-m-d');
    $title = "Listado de Activos - " . $currentDate;
    
    // Insert a new row before row 1
    $sheet->insertNewRowBefore(1, 1);
    
    // Merge cells A1 to F1 for the title
    $sheet->mergeCells('A1:F1');
    $sheet->setCellValue('A1', $title);
    
    // Style the title row
    $titleStyle = [
        'font' => ['bold' => true, 'size' => 14],
        'alignment' => ['horizontal' => Alignment::HORIZONTAL_LEFT, 'vertical' => Alignment::VERTICAL_CENTER],
    ];
    $sheet->getStyle('A1')->applyFromArray($titleStyle);
    $sheet->getRowDimension(1)->setRowHeight(20); // Set title row height
    
    // Set column headers
    $sheet->setCellValue('A2', 'Descripción');
    $sheet->setCellValue('B2', 'Marca');
    $sheet->setCellValue('C2', 'Modelo');
    $sheet->setCellValue('D2', 'Número Serie');
    $sheet->setCellValue('E2', 'Fecha Adquisición');
    $sheet->setCellValue('F2', 'Valor Activo');
    
    // Style the header row (now row 2)
    $headerStyle = [
        'font' => [
            'bold' => true,
        ],
        'alignment' => [
            'horizontal' => Alignment::HORIZONTAL_CENTER,
        ],
        'borders' => [
            'allBorders' => [
                'borderStyle' => Border::BORDER_THIN,
            ],
        ],
        'fill' => [
            'fillType' => Fill::FILL_SOLID,
            'startColor' => [
                'rgb' => 'DDDDDD',
            ],
        ],
    ];
    
    $sheet->getStyle('A2:F2')->applyFromArray($headerStyle);
    
    // Populate data rows
    $row = 3; // Start data from row 3
    foreach ($activos as $activo) {
        // Set cell values (without ID column)
        $sheet->setCellValue('A' . $row, $activo->getDescripcion() ?? '');
        $sheet->setCellValue('B' . $row, $activo->getMarca() ?? '');
        $sheet->setCellValue('C' . $row, $activo->getModelo() ?? '');
        $sheet->setCellValue('D' . $row, $activo->getNumeroSerie() ?? '');
        $sheet->setCellValue('E' . $row, $activo->getFechaAdquisicion() ?? '');
        
        // Format the valor_activo as currency
        $valorActivo = $activo->getValorActivo();
        if ($valorActivo !== null && $valorActivo > 0) {
            $sheet->setCellValue('F' . $row, '$' . number_format($valorActivo, 0, '', '.'));
        } else {
            $sheet->setCellValue('F' . $row, '');
        }
        
        $row++;
    }
    
    // Auto-size columns
    foreach (range('A', 'F') as $column) {
        $sheet->getColumnDimension($column)->setAutoSize(true);
    }
    
    // Apply borders to all data cells
    $lastRow = $row - 1;
    if ($lastRow >= 3) { // Start borders from data row 3
        $sheet->getStyle('A3:F' . $lastRow)->applyFromArray([
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_THIN,
                ],
            ],
        ]);
        
        // Center align fecha adquisicion column (now column E)
        $sheet->getStyle('E3:E' . $lastRow)->applyFromArray([
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_CENTER,
            ],
        ]);
        
        // Right align valor activo column (now column F)
        $sheet->getStyle('F3:F' . $lastRow)->applyFromArray([
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_RIGHT,
            ],
        ]);
    }
    
    // Create a temporary file
    $tempFile = tempnam(sys_get_temp_dir(), 'activos_export_');
    $writer = new Xlsx($spreadsheet);
    $writer->save($tempFile);
    
    // Set headers for download
    header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    header('Content-Disposition: attachment;filename="activos_export_' . date('Y-m-d_H-i-s') . '.xlsx"');
    header('Cache-Control: max-age=0');
    
    // Output the file
    readfile($tempFile);
    
    // Delete the temporary file
    unlink($tempFile);
    exit;
    
} catch (Exception $e) {
    // Handle errors
    error_log("Error exporting assets: " . $e->getMessage());
    $_SESSION['flash_message_error'] = 'Error al exportar los activos: ' . $e->getMessage();
    header('Location: lactivos');
    exit;
}
