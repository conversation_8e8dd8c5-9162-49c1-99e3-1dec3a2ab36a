document.addEventListener('DOMContentLoaded', function () {
    // Use event delegation on the table body
    const tableBody = document.getElementById('activo-table-body');
    const imagenesModal = document.getElementById('imagenes-modal');
    const imagenesModalLabel = document.getElementById('imagenes-modal-label');
    const imagenesContainer = document.getElementById('imagenes-container');
    const noImagenesMessage = document.getElementById('no-imagenes-message');
    const loadingImagenes = document.getElementById('loading-imagenes');
    
    // Initialize the modal
    const modal = new bootstrap.Modal(imagenesModal);
    
    // Handle export button click
    const exportBtn = document.getElementById('export-activos-btn');
    if (exportBtn) {
        exportBtn.addEventListener('click', function() {
            // Disable button and show loading state
            exportBtn.disabled = true;
            const originalText = exportBtn.innerHTML;
            exportBtn.innerHTML = '<i class="fa fa-spinner fa-spin fa-fw me-1"></i> Exportando...';
            
            // Create a form and submit it to trigger the download
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = 'export_activos';
            form.style.display = 'none';
            document.body.appendChild(form);
            form.submit();
            
            // Re-enable button after a delay
            setTimeout(function() {
                exportBtn.disabled = false;
                exportBtn.innerHTML = originalText;
                document.body.removeChild(form);
            }, 3000);
        });
    }
    
    if (tableBody) {
        tableBody.addEventListener('click', function (event) {
            const deactivateButton  = event.target.closest('.btn-desactivar-activo');
            const activateButton    = event.target.closest('.btn-activar-activo');
            const verImagenesButton = event.target.closest('.btn-ver-imagenes');
            
            // --- Handle Ver Imagenes Click ---
            if (verImagenesButton) {
                event.preventDefault();
                const activoId = verImagenesButton.dataset.activoid;
                const descripcion = verImagenesButton.dataset.descripcion || 'Activo';
                
                // Update modal title
                imagenesModalLabel.textContent = `Imágenes del Activo: ${descripcion}`;
                
                // Clear previous images
                imagenesContainer.innerHTML = '';
                
                // Show loading indicator
                loadingImagenes.classList.remove('d-none');
                noImagenesMessage.classList.add('d-none');
                
                // Show the modal
                modal.show();
                
                // Fetch images for this activo
                fetch(`get_activo_imagenes?id_activo=${activoId}`)
                    .then(response => response.json())
                    .then(data => {
                        // Hide loading indicator
                        loadingImagenes.classList.add('d-none');
                        
                        if (data.length === 0) {
                            // Show no images message
                            noImagenesMessage.classList.remove('d-none');
                        } else {
                            // Display the images
                            data.forEach(imagen => {
                                const imgContainer = document.createElement('div');
                                imgContainer.className = 'image-item';
                                
                                const img = document.createElement('img');
                                img.src = `resources/uploads/activos/${imagen.nombre_archivo}`;
                                img.alt = `Imagen ${imagen.id}`;
                                img.className = 'img-thumbnail';
                                img.style.maxWidth = '200px';
                                img.style.height = 'auto';
                                
                                imgContainer.appendChild(img);
                                imagenesContainer.appendChild(imgContainer);
                            });
                        }
                    })
                    .catch(error => {
                        console.error('Error fetching images:', error);
                        loadingImagenes.classList.add('d-none');
                        imagenesContainer.innerHTML = '<div class="alert alert-danger">Error al cargar las imágenes</div>';
                    });
            }
            
            // --- Handle Deactivate Click ---
            if (deactivateButton) {
                event.preventDefault();
                const activoId = deactivateButton.dataset.activoid;
                const descripcion = deactivateButton.dataset.descripcion || 'este activo';
                
                swal({
                    title     : "Confirmar Desactivación",
                    text      : `¿Seguro que quieres desactivar el activo '${descripcion}'?`,
                    icon      : "warning",
                    buttons   : {
                        cancel : {text: "Cancelar", value: null, visible: true, className: "", closeModal: true},
                        confirm: {text: "Confirmar", value: true, visible: true, className: "btn-danger", closeModal: true}
                    },
                    dangerMode: true,
                })
                    .then((willDelete) => {
                        if (willDelete) {
                            document.getElementById('deactivate-activo-id').value = activoId;
                            document.getElementById('deactivate-activo-form').submit();
                        }
                    });
            }
            
            // --- Handle Activate Click ---
            if (activateButton) {
                event.preventDefault();
                const activoId = activateButton.dataset.activoid;
                const descripcion = activateButton.dataset.descripcion || 'este activo';
                
                swal({
                    title     : "Confirmar Activación",
                    text      : `¿Seguro que quieres activar el activo '${descripcion}'?`,
                    icon      : "question",
                    buttons   : {
                        cancel : {text: "Cancelar", value: null, visible: true, className: "", closeModal: true},
                        confirm: {text: "Confirmar", value: true, visible: true, className: "btn-success", closeModal: true}
                    }
                })
                    .then((willActivate) => {
                        if (willActivate) {
                            document.getElementById('activate-activo-id').value = activoId;
                            document.getElementById('activate-activo-form').submit();
                        }
                    });
            }
        });
    }
});
